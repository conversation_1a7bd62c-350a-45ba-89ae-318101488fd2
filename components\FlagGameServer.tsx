import { Country } from "@/lib/data/countries";
import { generateQuestion, getDifficultySettings } from "@/lib/utils/gameLogic";
import FlagGameClient from "./FlagGameClient";

interface InitialGameData {
  currentCountry: Country;
  options: Country[];
  difficulty: "easy" | "medium" | "hard" | "expert";
  totalQuestions: number;
}

// Server-side question generation
const generateInitialQuestion = (difficulty: "easy" | "medium" | "hard" | "expert" = "easy"): InitialGameData => {
  const questionData = generateQuestion(difficulty);

  if (!questionData) {
    // Fallback to a basic question if generation fails
    throw new Error("Failed to generate initial question");
  }

  return {
    currentCountry: questionData.currentCountry,
    options: questionData.options,
    difficulty,
    totalQuestions: getDifficultySettings(difficulty).count,
  };
};

export default function FlagGameServer() {
  const initialGameData = generateInitialQuestion();
  
  return <FlagGameClient initialGameData={initialGameData} />;
}
